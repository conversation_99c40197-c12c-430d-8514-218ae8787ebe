# ORA Database Query Guide

This document provides comprehensive SQL queries for analyzing data in your ORA Hume EVI system database.

## 📊 Database Schema Overview

### Tables Structure
- **`users`** - User authentication and profile data
- **`chat_sessions`** - Chat session metadata and analytics
- **`conversation_messages`** - Individual messages with emotions and prosody
- **`audio_data`** - Audio file metadata (future use)
- **`session`** - Express session storage

---

## 🔌 Database Connection

### Connect to Database
```bash
# Using environment variables
PGPASSWORD=ora_secure_password_2024 psql -h 34.71.230.92 -U ora_user -d ora_hume_db

# Or using connection string
psql "****************************************************************/ora_hume_db"
```

### Quick Health Check
```sql
-- Verify all tables exist
\dt

-- Check database size
SELECT pg_size_pretty(pg_database_size('ora_hume_db')) as database_size;

-- Check table row counts
SELECT 
    schemaname,
    tablename,
    n_tup_ins as "rows"
FROM pg_stat_user_tables 
ORDER BY n_tup_ins DESC;
```

---

## 👥 User Analytics

### Basic User Queries
```sql
-- All users with registration info
SELECT 
    id,
    name,
    email,
    created_at,
    updated_at,
    profile_data
FROM users 
ORDER BY created_at DESC;

-- User count by registration date
SELECT 
    DATE(created_at) as registration_date,
    COUNT(*) as new_users
FROM users 
GROUP BY DATE(created_at)
ORDER BY registration_date DESC;

-- Most active users (by session count)
SELECT 
    u.name,
    u.email,
    COUNT(cs.id) as session_count,
    MAX(cs.started_at) as last_session
FROM users u
LEFT JOIN chat_sessions cs ON u.id = cs.user_id
GROUP BY u.id, u.name, u.email
ORDER BY session_count DESC;
```

### User Profile Analysis
```sql
-- Extract profile data details
SELECT 
    name,
    email,
    profile_data->>'picture' as profile_picture,
    profile_data->>'locale' as locale,
    profile_data->>'given_name' as first_name,
    profile_data->>'family_name' as last_name
FROM users;
```

---

## 💬 Chat Session Analytics

### Session Overview
```sql
-- All sessions with duration and status
SELECT 
    cs.id,
    u.name as user_name,
    cs.started_at,
    cs.ended_at,
    cs.status,
    EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at))/60 as duration_minutes,
    cs.hume_chat_group_id
FROM chat_sessions cs
JOIN users u ON cs.user_id = u.id
ORDER BY cs.started_at DESC;

-- Session statistics
SELECT 
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_sessions,
    COUNT(CASE WHEN status = 'error' THEN 1 END) as error_sessions,
    AVG(EXTRACT(EPOCH FROM (ended_at - started_at))/60) as avg_duration_minutes
FROM chat_sessions;

-- Sessions by date
SELECT 
    DATE(started_at) as session_date,
    COUNT(*) as session_count,
    AVG(EXTRACT(EPOCH FROM (ended_at - started_at))/60) as avg_duration_minutes
FROM chat_sessions 
WHERE started_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(started_at)
ORDER BY session_date DESC;
```

### Session Metadata Analysis
```sql
-- Extract session analytics from metadata
SELECT 
    cs.id,
    u.name,
    cs.started_at,
    (cs.metadata->>'finalStats')::jsonb->>'messageCount' as message_count,
    (cs.metadata->>'finalStats')::jsonb->>'duration' as duration,
    (cs.metadata->>'finalStats')::jsonb->>'audioOutputCount' as audio_outputs,
    cs.metadata->>'endReason' as end_reason
FROM chat_sessions cs
JOIN users u ON cs.user_id = u.id
WHERE cs.metadata IS NOT NULL
ORDER BY cs.started_at DESC;
```

---

## 🗨️ Message and Conversation Analysis

### Basic Message Queries
```sql
-- All messages with user context
SELECT 
    cm.id,
    u.name as user_name,
    cm.role,
    LEFT(cm.content, 100) as content_preview,
    cm.timestamp,
    cs.started_at as session_start
FROM conversation_messages cm
JOIN chat_sessions cs ON cm.session_id = cs.id
JOIN users u ON cs.user_id = u.id
ORDER BY cm.timestamp DESC
LIMIT 20;

-- Message count by role
SELECT 
    role,
    COUNT(*) as message_count,
    AVG(LENGTH(content)) as avg_message_length
FROM conversation_messages
GROUP BY role;

-- Messages per session
SELECT 
    cs.id as session_id,
    u.name as user_name,
    COUNT(cm.id) as total_messages,
    COUNT(CASE WHEN cm.role = 'user' THEN 1 END) as user_messages,
    COUNT(CASE WHEN cm.role = 'assistant' THEN 1 END) as assistant_messages
FROM chat_sessions cs
JOIN users u ON cs.user_id = u.id
LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
GROUP BY cs.id, u.name
ORDER BY total_messages DESC;
```

### Conversation Flow Analysis
```sql
-- Complete conversation for a session
SELECT 
    role,
    content,
    timestamp,
    CASE 
        WHEN emotions != '{}' THEN 'Has Emotions'
        ELSE 'No Emotions'
    END as emotion_status
FROM conversation_messages 
WHERE session_id = 'your-session-id-here'
ORDER BY timestamp;

-- Average response time (time between user message and assistant response)
WITH message_pairs AS (
    SELECT 
        cm1.timestamp as user_time,
        MIN(cm2.timestamp) as assistant_time,
        cm1.session_id
    FROM conversation_messages cm1
    JOIN conversation_messages cm2 ON cm1.session_id = cm2.session_id
    WHERE cm1.role = 'user' 
    AND cm2.role = 'assistant' 
    AND cm2.timestamp > cm1.timestamp
    GROUP BY cm1.id, cm1.timestamp, cm1.session_id
)
SELECT 
    session_id,
    AVG(EXTRACT(EPOCH FROM (assistant_time - user_time))) as avg_response_time_seconds
FROM message_pairs
GROUP BY session_id;
```

---

## 🎭 Emotion Analytics (The Crown Jewel!)

### Basic Emotion Queries
```sql
-- Messages with emotions
SELECT 
    cm.role,
    LEFT(cm.content, 50) as content_preview,
    cm.emotions,
    cm.timestamp
FROM conversation_messages cm
WHERE cm.emotions != '{}'
ORDER BY cm.timestamp DESC;

-- Count of messages with emotions
SELECT 
    COUNT(*) as total_messages,
    COUNT(CASE WHEN emotions != '{}' THEN 1 END) as messages_with_emotions,
    ROUND(
        COUNT(CASE WHEN emotions != '{}' THEN 1 END) * 100.0 / COUNT(*), 2
    ) as emotion_capture_percentage
FROM conversation_messages;
```

### Top Emotions Analysis
```sql
-- Extract and rank all emotions across all messages
WITH emotion_data AS (
    SELECT 
        jsonb_each_text(emotions) as emotion_pair
    FROM conversation_messages 
    WHERE emotions != '{}'
),
emotion_scores AS (
    SELECT 
        (emotion_pair).key as emotion,
        (emotion_pair).value::float as score
    FROM emotion_data
)
SELECT 
    emotion,
    COUNT(*) as occurrence_count,
    ROUND(AVG(score)::numeric, 4) as avg_score,
    ROUND(MAX(score)::numeric, 4) as max_score,
    ROUND(MIN(score)::numeric, 4) as min_score
FROM emotion_scores
GROUP BY emotion
ORDER BY avg_score DESC;
```

### User Emotion Profiles
```sql
-- Average emotions per user
WITH user_emotions AS (
    SELECT 
        u.name,
        u.email,
        cm.emotions
    FROM conversation_messages cm
    JOIN chat_sessions cs ON cm.session_id = cs.id
    JOIN users u ON cs.user_id = u.id
    WHERE cm.emotions != '{}' AND cm.role = 'user'
),
emotion_averages AS (
    SELECT 
        name,
        email,
        jsonb_each_text(emotions) as emotion_pair
    FROM user_emotions
)
SELECT 
    name,
    email,
    (emotion_pair).key as emotion,
    ROUND(AVG((emotion_pair).value::float)::numeric, 4) as avg_emotion_score
FROM emotion_averages
GROUP BY name, email, (emotion_pair).key
HAVING AVG((emotion_pair).value::float) > 0.1  -- Only significant emotions
ORDER BY name, avg_emotion_score DESC;
```

### Emotion Trends Over Time
```sql
-- Emotion changes during a conversation
SELECT 
    cm.timestamp,
    cm.emotions->>'joy' as joy,
    cm.emotions->>'excitement' as excitement,
    cm.emotions->>'calmness' as calmness,
    cm.emotions->>'anxiety' as anxiety,
    cm.emotions->>'satisfaction' as satisfaction
FROM conversation_messages cm
WHERE cm.session_id = 'your-session-id-here'
AND cm.emotions != '{}'
ORDER BY cm.timestamp;

-- Daily emotion averages
SELECT 
    DATE(cm.timestamp) as conversation_date,
    ROUND(AVG((cm.emotions->>'joy')::float)::numeric, 4) as avg_joy,
    ROUND(AVG((cm.emotions->>'excitement')::float)::numeric, 4) as avg_excitement,
    ROUND(AVG((cm.emotions->>'calmness')::float)::numeric, 4) as avg_calmness,
    ROUND(AVG((cm.emotions->>'anxiety')::float)::numeric, 4) as avg_anxiety,
    COUNT(*) as message_count
FROM conversation_messages cm
WHERE cm.emotions != '{}'
GROUP BY DATE(cm.timestamp)
ORDER BY conversation_date DESC;
```

---

## 🎵 Prosody and Audio Analysis

### Prosody Data Queries
```sql
-- Messages with prosody scores
SELECT
    cm.role,
    LEFT(cm.content, 50) as content_preview,
    cm.prosody_scores,
    cm.timestamp
FROM conversation_messages cm
WHERE cm.prosody_scores != '{}'
ORDER BY cm.timestamp DESC;

-- Compare emotions vs prosody
SELECT
    cm.id,
    cm.emotions->>'joy' as emotion_joy,
    cm.prosody_scores->>'joy' as prosody_joy,
    cm.emotions->>'excitement' as emotion_excitement,
    cm.prosody_scores->>'excitement' as prosody_excitement
FROM conversation_messages cm
WHERE cm.emotions != '{}' AND cm.prosody_scores != '{}'
ORDER BY cm.timestamp DESC;
```

### Audio Data Analysis
```sql
-- Audio file statistics
SELECT
    COUNT(*) as total_audio_files,
    AVG(duration) as avg_duration_ms,
    AVG(file_size) as avg_file_size_bytes,
    SUM(file_size) as total_storage_bytes
FROM audio_data;

-- Audio files by message
SELECT
    cm.role,
    cm.content,
    ad.duration,
    ad.file_size,
    ad.mime_type,
    ad.created_at
FROM audio_data ad
JOIN conversation_messages cm ON ad.message_id = cm.id
ORDER BY ad.created_at DESC;
```

---

## 📈 Advanced Analytics Queries

### Conversation Quality Metrics
```sql
-- Session engagement score (based on message count and emotions)
WITH session_metrics AS (
    SELECT
        cs.id as session_id,
        u.name,
        COUNT(cm.id) as total_messages,
        COUNT(CASE WHEN cm.emotions != '{}' THEN 1 END) as emotional_messages,
        AVG(LENGTH(cm.content)) as avg_message_length,
        EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at))/60 as duration_minutes
    FROM chat_sessions cs
    JOIN users u ON cs.user_id = u.id
    LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
    WHERE cs.status = 'completed'
    GROUP BY cs.id, u.name, cs.started_at, cs.ended_at
)
SELECT
    session_id,
    name,
    total_messages,
    emotional_messages,
    ROUND((emotional_messages * 100.0 / NULLIF(total_messages, 0))::numeric, 2) as emotion_percentage,
    ROUND(avg_message_length::numeric, 2) as avg_message_length,
    ROUND(duration_minutes::numeric, 2) as duration_minutes,
    -- Engagement score: weighted combination of metrics
    ROUND((
        (total_messages * 0.3) +
        (emotional_messages * 0.4) +
        (avg_message_length / 10 * 0.2) +
        (duration_minutes * 0.1)
    )::numeric, 2) as engagement_score
FROM session_metrics
ORDER BY engagement_score DESC;
```

### Emotion Correlation Analysis
```sql
-- Find emotions that often appear together
WITH emotion_pairs AS (
    SELECT
        cm.id,
        e1.key as emotion1,
        e1.value::float as score1,
        e2.key as emotion2,
        e2.value::float as score2
    FROM conversation_messages cm,
    LATERAL jsonb_each_text(cm.emotions) e1,
    LATERAL jsonb_each_text(cm.emotions) e2
    WHERE cm.emotions != '{}'
    AND e1.key < e2.key  -- Avoid duplicates
    AND e1.value::float > 0.1
    AND e2.value::float > 0.1
)
SELECT
    emotion1,
    emotion2,
    COUNT(*) as co_occurrence_count,
    ROUND(AVG(score1)::numeric, 4) as avg_score1,
    ROUND(AVG(score2)::numeric, 4) as avg_score2,
    ROUND(CORR(score1, score2)::numeric, 4) as correlation
FROM emotion_pairs
GROUP BY emotion1, emotion2
HAVING COUNT(*) >= 3  -- Only pairs that occur multiple times
ORDER BY correlation DESC;
```

### User Journey Analysis
```sql
-- Track user progression across sessions
WITH user_sessions AS (
    SELECT
        u.id as user_id,
        u.name,
        cs.id as session_id,
        cs.started_at,
        ROW_NUMBER() OVER (PARTITION BY u.id ORDER BY cs.started_at) as session_number,
        COUNT(cm.id) as message_count,
        AVG(CASE WHEN cm.emotions->>'satisfaction' IS NOT NULL
            THEN (cm.emotions->>'satisfaction')::float
            ELSE NULL END) as avg_satisfaction
    FROM users u
    JOIN chat_sessions cs ON u.id = cs.user_id
    LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
    WHERE cs.status = 'completed'
    GROUP BY u.id, u.name, cs.id, cs.started_at
)
SELECT
    user_id,
    name,
    session_number,
    started_at,
    message_count,
    ROUND(COALESCE(avg_satisfaction, 0)::numeric, 4) as avg_satisfaction,
    LAG(message_count) OVER (PARTITION BY user_id ORDER BY session_number) as prev_message_count,
    message_count - LAG(message_count) OVER (PARTITION BY user_id ORDER BY session_number) as message_growth
FROM user_sessions
ORDER BY user_id, session_number;
```

---

## 🔍 Diagnostic and Monitoring Queries

### System Health Checks
```sql
-- Recent activity summary
SELECT
    'Last 24 hours' as period,
    COUNT(DISTINCT u.id) as active_users,
    COUNT(cs.id) as new_sessions,
    COUNT(cm.id) as total_messages
FROM users u
LEFT JOIN chat_sessions cs ON u.id = cs.user_id
    AND cs.started_at >= NOW() - INTERVAL '24 hours'
LEFT JOIN conversation_messages cm ON cs.id = cm.session_id;

-- Error analysis
SELECT
    status,
    COUNT(*) as session_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER ()::numeric, 2) as percentage
FROM chat_sessions
GROUP BY status
ORDER BY session_count DESC;

-- Database growth tracking
SELECT
    'users' as table_name,
    COUNT(*) as row_count,
    pg_size_pretty(pg_total_relation_size('users')) as table_size
FROM users
UNION ALL
SELECT
    'chat_sessions',
    COUNT(*),
    pg_size_pretty(pg_total_relation_size('chat_sessions'))
FROM chat_sessions
UNION ALL
SELECT
    'conversation_messages',
    COUNT(*),
    pg_size_pretty(pg_total_relation_size('conversation_messages'))
FROM conversation_messages;
```

### Performance Monitoring
```sql
-- Identify long-running sessions
SELECT
    cs.id,
    u.name,
    cs.started_at,
    cs.ended_at,
    EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at))/3600 as duration_hours,
    COUNT(cm.id) as message_count
FROM chat_sessions cs
JOIN users u ON cs.user_id = u.id
LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
WHERE cs.ended_at IS NOT NULL
GROUP BY cs.id, u.name, cs.started_at, cs.ended_at
HAVING EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at))/3600 > 1  -- Sessions longer than 1 hour
ORDER BY duration_hours DESC;

-- Message frequency analysis
SELECT
    DATE_TRUNC('hour', timestamp) as hour,
    COUNT(*) as message_count,
    COUNT(DISTINCT session_id) as active_sessions
FROM conversation_messages
WHERE timestamp >= NOW() - INTERVAL '7 days'
GROUP BY DATE_TRUNC('hour', timestamp)
ORDER BY hour DESC;
```

---

## 🚀 Quick Reference Commands

### Essential Daily Queries
```sql
-- Today's activity
SELECT COUNT(*) FROM chat_sessions WHERE DATE(started_at) = CURRENT_DATE;
SELECT COUNT(*) FROM conversation_messages WHERE DATE(timestamp) = CURRENT_DATE;

-- Top emotions today
SELECT
    jsonb_object_keys(emotions) as emotion,
    COUNT(*) as count
FROM conversation_messages
WHERE DATE(timestamp) = CURRENT_DATE AND emotions != '{}'
GROUP BY jsonb_object_keys(emotions)
ORDER BY count DESC LIMIT 10;

-- Active users this week
SELECT COUNT(DISTINCT cs.user_id)
FROM chat_sessions cs
WHERE cs.started_at >= DATE_TRUNC('week', CURRENT_DATE);
```

### Export Data for Analysis
```sql
-- Export conversation data for external analysis
\copy (SELECT u.name, cs.started_at, cm.role, cm.content, cm.emotions FROM conversation_messages cm JOIN chat_sessions cs ON cm.session_id = cs.id JOIN users u ON cs.user_id = u.id ORDER BY cm.timestamp) TO 'conversation_export.csv' WITH CSV HEADER;

-- Export emotion summary
\copy (SELECT emotion, AVG(score::float) as avg_score FROM (SELECT jsonb_each_text(emotions) as emotion_data FROM conversation_messages WHERE emotions != '{}') t, LATERAL (SELECT (emotion_data).key as emotion, (emotion_data).value as score) e GROUP BY emotion ORDER BY avg_score DESC) TO 'emotion_summary.csv' WITH CSV HEADER;
```
```
