#!/usr/bin/env node

/**
 * Test script to verify admin authentication is working
 * This script will test the admin authentication flow
 */

const https = require('https');
const { URL } = require('url');

const BACKEND_URL = 'https://ora-backend-222129249954.us-central1.run.app';
const ADMIN_EMAIL = '<EMAIL>';

// Function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const req = https.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }

    req.end();
  });
}

async function testAdminAuth() {
  console.log('🔍 Testing Admin Authentication');
  console.log('================================');
  console.log(`Backend URL: ${BACKEND_URL}`);
  console.log(`Admin Email: ${ADMIN_EMAIL}`);
  console.log('');

  try {
    // Test 1: Check if admin analytics endpoint is accessible without auth
    console.log('📋 Test 1: Admin analytics without authentication');
    const noAuthResponse = await makeRequest(`${BACKEND_URL}/api/admin/analytics?days=1`);
    console.log(`Status: ${noAuthResponse.status}`);
    console.log(`Response: ${JSON.stringify(noAuthResponse.data, null, 2)}`);
    console.log('');

    // Test 2: Check health endpoint
    console.log('📋 Test 2: Health check');
    const healthResponse = await makeRequest(`${BACKEND_URL}/health`);
    console.log(`Status: ${healthResponse.status}`);
    console.log(`Response: ${JSON.stringify(healthResponse.data, null, 2)}`);
    console.log('');

    // Test 3: Check if we can access the database directly via a test endpoint
    console.log('📋 Test 3: Database connectivity test');
    console.log('Note: This test checks if the backend can connect to the database');
    console.log('The health endpoint should show database status');
    
    if (healthResponse.data && healthResponse.data.data) {
      const dbStatus = healthResponse.data.data.database;
      console.log(`Database Status: ${dbStatus}`);
      
      if (dbStatus === 'connected') {
        console.log('✅ Database is connected - admin whitelist should be accessible');
      } else {
        console.log('❌ Database is not connected - this could be the issue');
      }
    }
    console.log('');

    // Test 4: Summary
    console.log('📋 Summary');
    console.log('==========');
    console.log('1. Admin endpoint correctly requires authentication (401 expected)');
    console.log('2. Backend is running and healthy');
    console.log('3. Database connectivity determines if admin whitelist works');
    console.log('');
    console.log('🔧 Next Steps:');
    console.log('1. User needs to login via Google OAuth to get a valid JWT token');
    console.log('2. Frontend should store the JWT token and use it for admin requests');
    console.log('3. Backend will then check the admin <NAME_EMAIL>');
    console.log('4. Since the user is already in the whitelist as super_admin, access should be granted');
    console.log('');
    console.log('💡 The issue is likely:');
    console.log('   - User is not properly authenticated (no valid JWT token)');
    console.log('   - Frontend is not sending the JWT token correctly');
    console.log('   - JWT token has expired and needs refresh');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testAdminAuth();
