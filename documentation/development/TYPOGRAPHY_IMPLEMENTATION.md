# ORA Typography Implementation

## Overview

The ORA application now uses a dual-font typography system:
- **Georgia** for headings (h1, h2, h3, h4, h5, h6)
- **Inter** for body text (paragraphs, buttons, inputs, labels, etc.)

## Implementation Details

### 1. Font Loading

Fonts are loaded via Google Fonts in `index.html`:
```html
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
```

Note: Georgia is a system font and doesn't require external loading.

### 2. Tailwind Configuration

Font families are defined in `tailwind.config.js`:
```javascript
fontFamily: {
  'heading': ['Georgia', 'Times New Roman', 'serif'],
  'body': ['Inter', 'system-ui', 'sans-serif'],
  'sans': ['Inter', 'system-ui', 'sans-serif'],
  'serif': ['Georgia', 'Times New Roman', 'serif']
}
```

### 3. CSS Implementation

Global typography rules in `src/index.css`:
```css
/* Typography - Headings use Georgia, body text uses Inter */
h1, h2, h3, h4, h5, h6 {
  @apply font-heading;
}

p, span, div, a, button, input, textarea, label {
  @apply font-body;
}
```

## Available Classes

### Font Family Classes
- `font-heading` - Georgia serif font for headings
- `font-body` - Inter sans-serif font for body text
- `font-sans` - Inter (alias for body font)
- `font-serif` - Georgia (alias for heading font)

### Utility Classes
- `.text-heading` - Apply Georgia font to any element
- `.text-body` - Apply Inter font to any element

## Usage Examples

### Automatic Application
All heading elements automatically use Georgia:
```jsx
<h1>This uses Georgia font</h1>
<h2>This also uses Georgia font</h2>
```

All body elements automatically use Inter:
```jsx
<p>This uses Inter font</p>
<button>This button text uses Inter</button>
```

### Manual Application
Force a specific font on any element:
```jsx
<div className="font-heading">This div uses Georgia</div>
<span className="font-body">This span uses Inter</span>
```

### Utility Classes
```jsx
<div className="text-heading">Georgia font via utility class</div>
<div className="text-body">Inter font via utility class</div>
```

## Font Characteristics

### Georgia (Headings)
- **Type**: Serif
- **Style**: Classic, elegant, professional
- **Best for**: Titles, headings, emphasis
- **Readability**: Excellent on screens
- **Fallbacks**: Times New Roman, serif

### Inter (Body Text)
- **Type**: Sans-serif
- **Style**: Modern, clean, highly legible
- **Best for**: Body text, UI elements, forms
- **Readability**: Optimized for user interfaces
- **Fallbacks**: system-ui, sans-serif

## Testing

A typography test page is available at `/typography-test` to showcase:
- All heading levels (H1-H6) with Georgia
- Various body text sizes with Inter
- Interactive elements (buttons, forms)
- Font implementation details

## Browser Support

- **Georgia**: Universal system font support
- **Inter**: Loaded via Google Fonts with system fallbacks
- **Fallbacks**: Comprehensive fallback chains ensure compatibility

## Performance Considerations

- Georgia is a system font (no download required)
- Inter is preloaded for optimal performance
- Font display: swap for better loading experience
- Fallback fonts ensure immediate text rendering

## Customization

To modify fonts, update:
1. `tailwind.config.js` - Font family definitions
2. `src/index.css` - Global typography rules
3. `index.html` - Font loading (if changing to different web fonts)

## Migration Notes

- All existing headings automatically use Georgia
- All existing body text automatically uses Inter
- No component changes required
- Backward compatible with existing styles
