#!/usr/bin/env node

/**
 * Test script to verify the server can start without database connection
 * This simulates the Cloud Run environment where database might not be immediately available
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const TEST_PORT = 3333;
const TIMEOUT = 10000; // 10 seconds

console.log('🧪 Testing server startup without database connection...');

// Set environment variables to simulate Cloud Run
const env = {
  ...process.env,
  PORT: TEST_PORT,
  NODE_ENV: 'production',
  // Intentionally invalid database URL to test graceful handling
  DATABASE_URL: '*****************************************/invalid'
};

// Start the server
const serverProcess = spawn('node', [join(__dirname, '../backend/dist/server.js')], {
  env,
  stdio: 'pipe'
});

let serverStarted = false;
let healthCheckPassed = false;

// Listen for server output
serverProcess.stdout.on('data', (data) => {
  const output = data.toString();
  console.log('📝 Server output:', output.trim());
  
  if (output.includes(`Server running on port ${TEST_PORT}`)) {
    serverStarted = true;
    console.log('✅ Server started successfully');
    
    // Test health check endpoint
    setTimeout(testHealthCheck, 2000);
  }
});

serverProcess.stderr.on('data', (data) => {
  const output = data.toString();
  console.log('⚠️  Server stderr:', output.trim());
});

// Test health check endpoint
async function testHealthCheck() {
  try {
    const response = await fetch(`http://localhost:${TEST_PORT}/health`);
    const data = await response.json();
    
    console.log('🏥 Health check response:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success) {
      healthCheckPassed = true;
      console.log('✅ Health check passed');
      
      if (data.data.database === 'disconnected') {
        console.log('✅ Server correctly handles database disconnection');
      }
    } else {
      console.log('❌ Health check failed');
    }
  } catch (error) {
    console.log('❌ Health check error:', error.message);
  }
  
  // Clean up
  cleanup();
}

// Cleanup function
function cleanup() {
  console.log('🧹 Cleaning up...');
  serverProcess.kill('SIGTERM');
  
  setTimeout(() => {
    if (serverStarted && healthCheckPassed) {
      console.log('🎉 Test passed! Server can start without database connection.');
      process.exit(0);
    } else {
      console.log('❌ Test failed!');
      process.exit(1);
    }
  }, 1000);
}

// Timeout handler
setTimeout(() => {
  console.log('⏰ Test timeout reached');
  cleanup();
}, TIMEOUT);

// Handle process termination
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);
