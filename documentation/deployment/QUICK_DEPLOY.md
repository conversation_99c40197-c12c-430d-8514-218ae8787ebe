# Quick Deployment Reference

This is a condensed reference for deploying ORA Hume to Google Cloud Run after initial setup.

## Prerequisites Checklist

- [ ] Google Cloud Project created
- [ ] gcloud CLI installed and authenticated
- [ ] Required APIs enabled (Cloud Run, Cloud Build, Secret Manager, Cloud SQL)
- [ ] PostgreSQL database set up (Cloud SQL recommended)

## Quick Deploy Steps

### 1. Set Environment Variables (First Time Only)

```bash
# Run the setup script
chmod +x scripts/setup-cloud-env.sh
./scripts/setup-cloud-env.sh YOUR_PROJECT_ID

# Or manually create secrets:
echo -n "postgresql://ora_user:@YOUR_DB_IP:5432/ora_hume_db" | gcloud secrets create DATABASE_URL --data-file=-
echo -n "your-google-client-id" | gcloud secrets create GOOGLE_CLIENT_ID --data-file=-
echo -n "your-google-client-secret" | gcloud secrets create GOOGLE_CLIENT_SECRET --data-file=-
echo -n "your-jwt-secret" | gcloud secrets create JWT_SECRET --data-file=-
echo -n "your-session-secret" | gcloud secrets create SESSION_SECRET --data-file=-
echo -n "your-hume-api-key" | gcloud secrets create HUME_API_KEY --data-file=-
echo -n "your-hume-secret-key" | gcloud secrets create HUME_SECRET_KEY --data-file=-
echo -n "your-hume-config-id" | gcloud secrets create HUME_CONFIG_ID --data-file=-
```

### 2. Deploy

```bash
# From project root
gcloud builds submit --config=cloudbuild.yaml
```

### 3. Verify Deployment

```bash
# Check health
curl https://ora-backend-YOUR_PROJECT_ID.a.run.app/health

# Check logs if needed
gcloud logs read --service=ora-backend --region=us-central1 --limit=20
```

## Database Setup (Cloud SQL)

```bash
# Create instance
gcloud sql instances create ora-db \
    --database-version=POSTGRES_14 \
    --tier=db-f1-micro \
    --region=us-central1

# Create database and user
gcloud sql databases create ora_hume_db --instance=ora-db
gcloud sql users create ora_user --instance=ora-db

# Get IP for DATABASE_URL
gcloud sql instances describe ora-db --format="value(ipAddresses[0].ipAddress)"
```

## Common Commands

```bash
# Update a secret
echo -n "new-value" | gcloud secrets versions add SECRET_NAME --data-file=-

# View all secrets
gcloud secrets list

# Check Cloud Run services
gcloud run services list --region=us-central1

# View service details
gcloud run services describe ora-backend --region=us-central1

# Update service (if needed)
gcloud run services update ora-backend --region=us-central1 --memory=2Gi
```

## Troubleshooting Quick Fixes

```bash
# If deployment fails, check build logs
gcloud builds list --limit=5
gcloud builds log BUILD_ID

# If service won't start, check logs
gcloud logs read --service=ora-backend --region=us-central1 --limit=50

# Test database connection format
# Should be: postgresql://ora_user:@IP_ADDRESS:5432/ora_hume_db
```

## URLs After Deployment

- **Backend**: `https://ora-backend-YOUR_PROJECT_ID.a.run.app`
- **Frontend**: `https://ora-frontend-YOUR_PROJECT_ID.a.run.app`
- **Health Check**: `https://ora-backend-YOUR_PROJECT_ID.a.run.app/health`

## Key Files

- `cloudbuild.yaml` - Build and deployment configuration
- `backend/Dockerfile` - Backend container configuration
- `frontend/Dockerfile` - Frontend container configuration
- `DEPLOYMENT.md` - Full deployment guide
- `scripts/setup-cloud-env.sh` - Environment setup script
