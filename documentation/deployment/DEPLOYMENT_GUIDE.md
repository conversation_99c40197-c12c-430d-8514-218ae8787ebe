# ORA Hume - Simple GCP Deployment Guide

## 🚀 Quick Deployment to Google Cloud Platform

This guide will help you deploy your ORA Hume application to GCP in under 30 minutes.

### Prerequisites

1. **Google Cloud Account** with billing enabled
2. **Google Cloud SDK** installed locally
3. **Docker** installed locally
4. **Your API Credentials**:
   - Google OAuth Client ID & Secret
   - Hume AI API Key & Config ID

### Step 1: Setup GCP Project

```bash
# Install gcloud CLI if not already installed
# https://cloud.google.com/sdk/docs/install

# Login to GCP
gcloud auth login

# Create a new project (or use existing)
gcloud projects create your-ora-project-id
gcloud config set project your-ora-project-id

# Enable billing for the project (required for Cloud SQL)
# Go to: https://console.cloud.google.com/billing
```

### Step 2: Deploy to GCP

```bash
# Clone your repository and navigate to it
cd ORA-P1

# Run the deployment script
./deploy-gcp.sh your-ora-project-id
```

The script will:
- Enable required GCP APIs
- Create a Cloud SQL PostgreSQL instance
- Build and deploy your application to Cloud Run
- Set up service accounts and permissions

### Step 3: Configure Environment Variables

After deployment, you need to set your actual credentials:

```bash
# Set backend environment variables
gcloud run services update ora-backend --region=us-central1 \
  --set-env-vars \
  GOOGLE_CLIENT_ID=your_actual_google_client_id,\
  GOOGLE_CLIENT_SECRET=your_actual_google_client_secret,\
  HUME_API_KEY=your_actual_hume_api_key,\
  HUME_SECRET_KEY=your_actual_hume_secret_key,\
  HUME_CONFIG_ID=your_actual_hume_config_id,\
  JWT_SECRET=your_random_jwt_secret_here,\
  SESSION_SECRET=your_random_session_secret_here
```

### Step 4: Setup Database

```bash
# Connect to your Cloud SQL instance
gcloud sql connect ora-postgres --user=postgres

# Run the setup script
\i setup-gcp-database.sql

# Exit
\q
```

### Step 5: Update OAuth Settings

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services > Credentials
3. Edit your OAuth 2.0 Client ID
4. Add your Cloud Run URLs to authorized origins and redirect URIs:
   - Authorized JavaScript origins: `https://your-frontend-url`
   - Authorized redirect URIs: `https://your-backend-url/api/auth/google/callback`

### Step 6: Test Your Deployment

Visit your frontend URL and test:
- Google OAuth login
- Voice chat functionality
- Database persistence

## 📊 What You Get

### Infrastructure:
- **Cloud Run**: Serverless containers that auto-scale
- **Cloud SQL**: Managed PostgreSQL database
- **Cloud Build**: Automated CI/CD pipeline

### Costs (Estimated Monthly):
- **Cloud Run**: $5-15 (very low with 5 concurrent users)
- **Cloud SQL**: $50 (db-n1-standard-1 instance)
- **Total**: ~$70-100/month

### Performance:
- **Auto-scaling**: 0 to 10 instances based on demand
- **Cold starts**: ~2-3 seconds (acceptable for PoC)
- **Concurrent users**: Easily handles 5 concurrent sessions
- **Database**: 100 connections available

## 🔧 Management Commands

### View logs:
```bash
gcloud run services logs read ora-backend --region=us-central1
gcloud run services logs read ora-frontend --region=us-central1
```

### Update deployment:
```bash
# After making code changes, just run:
gcloud builds submit --config=cloudbuild.yaml
```

### Scale services:
```bash
# Increase max instances if needed
gcloud run services update ora-backend --region=us-central1 --max-instances=20
```

### Connect to database:
```bash
gcloud sql connect ora-postgres --user=postgres
```

## 🚨 Troubleshooting

### Common Issues:

1. **"Service account does not exist"**
   - Run the deployment script again, it will create missing resources

2. **"Database connection failed"**
   - Check that Cloud SQL instance is running
   - Verify DATABASE_URL format in environment variables

3. **"OAuth error"**
   - Ensure redirect URIs are correctly set in Google Cloud Console
   - Check that GOOGLE_CLIENT_ID matches in both frontend and backend

4. **"Hume API error"**
   - Verify your Hume API credentials
   - Check that you have access to EVI features

### Get help:
```bash
# Check service status
gcloud run services describe ora-backend --region=us-central1
gcloud run services describe ora-frontend --region=us-central1

# Check recent deployments
gcloud run revisions list --service=ora-backend --region=us-central1
```

## 🎯 Next Steps

Once deployed and working:
1. **Custom Domain**: Add your own domain name
2. **SSL Certificate**: Automatic with Cloud Run
3. **Monitoring**: Enable Cloud Monitoring for insights
4. **Backup**: Cloud SQL automatic backups are enabled
5. **Scaling**: Increase limits if you get more users

This setup is perfect for a PoC and can easily handle 100 users with 5 concurrent sessions!
