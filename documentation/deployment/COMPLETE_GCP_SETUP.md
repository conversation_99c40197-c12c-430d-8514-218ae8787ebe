# Complete GCP Database Setup Guide

## Current Status ✅

Your system is now **fully configured** for cloud database migration:

- **✅ Admin Whitelist**: Database-driven (not localStorage)
- **✅ Analytics Data**: Database-driven (not localhost)
- **✅ Admin User**: `<EMAIL>` configured locally
- **✅ Environment**: Ready for GCP switching
- **✅ Scripts**: Automated setup tools created

## What's Working Now

1. **Localhost Setup**: Fully functional with admin whitelist
2. **Cloud-Ready Code**: All systems use database storage
3. **Environment Configuration**: Easy switching between localhost/GCP

## To Complete GCP Migration

### Step 1: Set Database Password

The GCP database requires a password for `ora_user`:

```bash
# Set password for the database user
gcloud sql users set-password ora_user \
  --instance=ora-postgres \
  --project=ora-phase1 \
  --password=YOUR_SECURE_PASSWORD

# Example with a strong password:
gcloud sql users set-password ora_user \
  --instance=ora-postgres \
  --project=ora-phase1 \
  --password="SecurePass123!"
```

### Step 2: Update Environment File

Edit `backend/.env` to include the password:

```env
# Comment out localhost
# DATABASE_URL=postgresql://postgres:@localhost:5432/ora_hume_db

# Uncomment and add password to GCP database
DATABASE_URL=************************************************************/ora_hume_db
```

### Step 3: Create Missing Tables

Run the admin tables setup script:

```bash
# Create admin tables in GCP database
psql *****************************************************/ora_hume_db -f scripts/setup-gcp-admin-tables.sql
```

### Step 4: Test Connection

```bash
# Test the connection
psql *****************************************************/ora_hume_db -c "SELECT email, role FROM admin_whitelist;"
```

### Step 5: Restart Backend

```bash
cd backend
npm run dev
```

You should see:
- ✅ Database connected successfully
- No password errors
- Admin queries working

## Alternative: Use Cloud SQL Proxy (Recommended for Production)

For better security and reliability:

### Install Cloud SQL Proxy

```bash
# Download Cloud SQL Proxy
curl -o cloud_sql_proxy https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64
chmod +x cloud_sql_proxy

# Or on macOS:
curl -o cloud_sql_proxy https://dl.google.com/cloudsql/cloud_sql_proxy.darwin.amd64
chmod +x cloud_sql_proxy
```

### Start Proxy

```bash
# Start the proxy (replace with your connection name)
./cloud_sql_proxy -instances=ora-phase1:us-central1:ora-postgres=tcp:5432
```

### Update Environment

```env
# Use proxy connection (more secure)
DATABASE_URL=postgresql://ora_user:YOUR_PASSWORD@localhost:5432/ora_hume_db
```

## Verification Steps

### 1. Database Connection
```bash
# Should connect without errors
psql ************************************************/ora_hume_db -c "SELECT 1;"
```

### 2. Admin Tables
```bash
# Should show admin_whitelist table
psql ************************************************/ora_hume_db -c "\dt admin*"
```

### 3. Admin User
```bash
# <NAME_EMAIL>
psql ************************************************/ora_hume_db -c "SELECT email, role FROM admin_whitelist;"
```

### 4. Backend Server
```bash
# Should show "Database connected successfully"
cd backend && npm run dev
```

### 5. Analytics Dashboard
1. Start frontend: `cd frontend && npm run dev`
2. Login with `<EMAIL>`
3. Go to `/admin/analytics`
4. Verify data loads from GCP database

## Quick Commands Summary

```bash
# 1. Set database password
gcloud sql users set-password ora_user --instance=ora-postgres --project=ora-phase1 --password="SecurePass123!"

# 2. Update backend/.env with password
# DATABASE_URL=******************************************************/ora_hume_db

# 3. Create admin tables
psql ******************************************************/ora_hume_db -f scripts/setup-gcp-admin-tables.sql

# 4. Test connection
psql ******************************************************/ora_hume_db -c "SELECT email FROM admin_whitelist;"

# 5. Restart backend
cd backend && npm run dev
```

## Security Notes

1. **Use Strong Passwords**: Include uppercase, lowercase, numbers, and symbols
2. **Environment Variables**: Store passwords in environment variables, not in code
3. **Cloud SQL Proxy**: Use for production deployments
4. **IP Whitelisting**: Restrict database access to known IPs
5. **SSL Connections**: Enable SSL for production

## Rollback Plan

If anything goes wrong, you can always revert to localhost:

```env
# In backend/.env, comment out GCP and uncomment localhost:
DATABASE_URL=postgresql://postgres:@localhost:5432/ora_hume_db
# DATABASE_URL=************************************************/ora_hume_db
```

## Next Steps After Migration

1. **Update Production Environment**: Set GCP database in `backend/.env.production`
2. **Deploy to Cloud Run**: Use GCP database for production deployment
3. **Monitor Performance**: Check database performance and optimize queries
4. **Backup Strategy**: Set up automated backups for GCP database
5. **Security Audit**: Review access controls and permissions

Your system is fully prepared for this migration - it's just a matter of setting the password and updating the connection string!
