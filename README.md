# ORA - Empathic Voice Interface

A production-ready voice AI application built with <PERSON>'s Empathic Voice Interface (EVI), featuring Google SSO authentication, real-time emotion analysis, and comprehensive conversation management.

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React Frontend │────│  Express Backend │────│   PostgreSQL    │
│   - Auth UI      │    │  - Google SSO    │    │   - Users       │
│   - Chat Interface│    │  - Session Mgmt  │    │   - Sessions    │
│   - Profile Page │    │  - E<PERSON> Proxy     │    │   - Conversations│
└─────────────────┘    │  - Webhooks      │    │   - Emotions    │
                       │  - APIs          │    │   - Transcripts │
                       └──────────────────┘    └─────────────────┘
                              │
                       ┌──────────────────┐
                       │   Hume EVI API   │
                       │   - Voice Chat   │
                       │   - Emotions     │
                       │   - Transcripts  │
                       └──────────────────┘
```

## ✨ Features

### 🔐 Authentication & User Management
- **Google SSO Integration** - Secure OAuth 2.0 authentication
- **JWT Session Management** - Stateless authentication with refresh tokens
- **User Profiles** - Customizable user preferences and demographics
- **Admin Analytics** - Comprehensive admin dashboard with user insights

### 💬 Empathic Voice Chat
- **Real-time Voice Conversations** - WebSocket-based communication with Hume EVI
- **Emotion Analysis** - Live emotion detection and scoring (48 emotions)
- **Voice Storage** - Cloud-based voice recording storage
- **Advanced UI** - 3D orb interface with emotion-driven visual effects

### 📊 Data & Analytics
- **Emotion Analytics** - Detailed emotion insights and transition analysis
- **Conversation History** - Complete conversation transcripts and metadata
- **User Cohorts** - Age-based user analytics and demographics
- **Cloud Storage** - Google Cloud integration for scalable data storage

### 🚀 Production Ready
- **Scalable Architecture** - Cloud-native design with Google Cloud Run
- **Custom Domains** - Professional domain setup (talktoora.com)
- **Security** - Helmet.js, CORS, rate limiting, input validation
- **CI/CD Pipeline** - Automated deployment with Cloud Build

## 🛠️ Technology Stack

### Backend
- **Node.js + Express** - RESTful API server
- **TypeScript** - Type-safe development
- **PostgreSQL** - Primary database with JSONB support
- **WebSocket** - Real-time communication
- **Google OAuth 2.0** - Authentication
- **JWT** - Session management
- **Hume SDK** - EVI integration
- **Google Cloud Storage** - Voice file storage

### Frontend
- **React 18** - Modern UI framework
- **TypeScript** - Type-safe frontend
- **Tailwind CSS** - Utility-first styling
- **shadcn/ui** - Modern component library
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **WebSocket** - Real-time updates

### Infrastructure
- **Google Cloud Run** - Serverless container platform
- **Google Cloud SQL** - Managed PostgreSQL
- **Google Cloud Storage** - File storage
- **Docker** - Containerization
- **Cloud Build** - CI/CD pipeline

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL 13+
- Google Cloud Console account
- Hume AI account

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd ORA-P1

# Make startup script executable
chmod +x start-dev.sh

# Run automated setup
./start-dev.sh
```

### 2. Configure Environment Variables

#### Backend Configuration
```bash
cd backend
cp .env.example .env
# Edit .env with your credentials
```

#### Frontend Configuration
```bash
cd frontend
cp .env.example .env
# Edit .env with your API URLs
```

### 3. Access the Application

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **Health Check**: http://localhost:3001/health

## 📚 Documentation

For detailed setup, deployment, and configuration instructions, see the [documentation](./documentation/) folder:

### Setup & Configuration
- [Complete Setup Guide](./documentation/setup/ENVIRONMENT_SETUP_GUIDE.md) - Detailed environment setup
- [Custom Domain Setup](./documentation/setup/CUSTOM_DOMAIN_SETUP.md) - Domain configuration
- [Backend Testing Guide](./documentation/setup/CUSTOM_BACKEND_TESTING_GUIDE.md) - Testing procedures

### Deployment
- [Deployment Guide](./documentation/deployment/DEPLOYMENT_GUIDE.md) - Complete deployment instructions
- [Quick Deploy](./documentation/deployment/QUICK_DEPLOY.md) - Fast deployment reference
- [GCP Migration Guide](./documentation/deployment/GCP_MIGRATION_GUIDE.md) - Cloud migration steps

### Authentication & OAuth
- [OAuth Setup](./documentation/oauth/OAUTH_BRANDING_SETUP.md) - Google OAuth configuration
- [OAuth Domain Updates](./documentation/oauth/OAUTH_DOMAIN_UPDATE.md) - Domain configuration
- [OAuth Troubleshooting](./documentation/oauth/OAUTH_FIX_GUIDE.md) - Common issues

### Analytics & Features
- [Database Query Guide](./documentation/guides/DATABASE_QUERY_GUIDE.md) - Database operations
- [Voice Storage Guide](./documentation/guides/VOICE_STORAGE_GUIDE.md) - Voice recording setup
- [Advanced Analytics](./documentation/analytics/ADVANCED_ADMIN_ANALYTICS_DESIGN.md) - Analytics dashboard

## 🔧 Configuration

### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3001/api/auth/google/callback` (development)
   - `https://yourdomain.com/api/auth/google/callback` (production)

### Hume AI Setup
1. Sign up at [Hume AI](https://hume.ai/)
2. Get your API key and secret
3. Create an EVI configuration
4. Note your config ID

## 📚 API Documentation

### Authentication Endpoints
- `GET /api/auth/google` - Redirect to Google OAuth
- `GET /api/auth/google/callback` - Handle OAuth callback
- `POST /api/auth/google/verify` - Verify Google ID token
- `POST /api/auth/refresh` - Refresh access token
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update user profile
- `POST /api/auth/logout` - Logout user

### Chat Endpoints
- `GET /api/chat/sessions` - Get user's chat sessions
- `GET /api/chat/sessions/:id` - Get specific session
- `GET /api/chat/sessions/:id/messages` - Get session messages
- `GET /api/chat/sessions/:id/transcript` - Get session transcript
- `GET /api/chat/sessions/:id/emotions` - Get emotion analytics
- `DELETE /api/chat/sessions/:id` - Delete session
- `GET /api/chat/analytics` - Get user analytics

### Admin Endpoints
- `GET /api/admin/analytics` - Get admin analytics dashboard
- `GET /api/admin/users` - Get user management data
- `GET /api/admin/emotions` - Get emotion analytics

### WebSocket Events
- `start_chat` - Start new chat session
- `end_chat` - End current session
- `audio_input` - Send audio data
- `hume_message` - Receive Hume responses
- `error` - Error notifications

## 🗄️ Database Schema

### Users Table
```sql
users (
  id: UUID PRIMARY KEY,
  google_id: VARCHAR UNIQUE,
  email: VARCHAR UNIQUE,
  name: VARCHAR,
  profile_data: JSONB,
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
)
```

### Chat Sessions Table
```sql
chat_sessions (
  id: UUID PRIMARY KEY,
  user_id: UUID REFERENCES users(id),
  hume_chat_group_id: VARCHAR,
  started_at: TIMESTAMP,
  ended_at: TIMESTAMP,
  status: VARCHAR,
  metadata: JSONB
)
```

### Conversation Messages Table
```sql
conversation_messages (
  id: UUID PRIMARY KEY,
  session_id: UUID REFERENCES chat_sessions(id),
  role: VARCHAR,
  content: TEXT,
  timestamp: TIMESTAMP,
  emotions: JSONB,
  prosody_scores: JSONB,
  metadata: JSONB
)
```

## 🚀 Deployment

### Quick Deploy to Google Cloud Run

```bash
# Set up environment variables (first time only)
./scripts/setup-cloud-env.sh YOUR_PROJECT_ID

# Deploy
gcloud builds submit --config=cloudbuild.yaml
```

For detailed deployment instructions, see the [deployment documentation](./documentation/deployment/).

## 🔒 Security Features

- **Authentication**: Google OAuth 2.0 + JWT
- **Authorization**: Role-based access control with admin whitelist
- **Data Protection**: Encrypted sensitive data
- **Rate Limiting**: API endpoint protection
- **Input Validation**: Comprehensive request validation
- **CORS**: Configured cross-origin policies
- **Security Headers**: Helmet.js implementation

## 📈 Monitoring & Analytics

- **Health Checks**: Built-in health monitoring
- **Error Tracking**: Comprehensive error logging
- **Performance Metrics**: Response time tracking
- **User Analytics**: Conversation and emotion insights
- **Admin Dashboard**: Comprehensive analytics with emotion flow visualization

## 🆘 Support

For support and questions:
- Review the [documentation](./documentation/) folder
- Check the [setup guides](./documentation/setup/)
- Review the [troubleshooting guides](./documentation/oauth/)
- Verify environment configuration

## 📄 License

This project is licensed under the MIT License.

---

**Built with ❤️ using Hume AI's Empathic Voice Interface**

*A production-ready voice AI application with advanced emotion analysis and comprehensive analytics.*
