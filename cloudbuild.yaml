# Cloud Build configuration for ORA Hume deployment
steps:
  # Build backend (from root directory to include shared folder)
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/ora-backend:$BUILD_ID', '-f', 'backend/Dockerfile', '.']

  # Build frontend (from root directory to include shared folder)
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/ora-frontend:$BUILD_ID', '-f', 'frontend/Dockerfile', '.']

  # Push backend image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/ora-backend:$BUILD_ID']

  # Push frontend image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/ora-frontend:$BUILD_ID']
    
  # Deploy backend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'ora-backend'
      - '--image'
      - 'gcr.io/$PROJECT_ID/ora-backend:$BUILD_ID'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--set-env-vars'
      - 'NODE_ENV=production,BACKEND_URL=https://api.talktoora.com,FRONTEND_URL=https://talktoora.com,CORS_ORIGINS=https://ora-frontend-222129249954.us-central1.run.app https://talktoora.com,DEFAULT_ADMIN_EMAIL=<EMAIL>'
      - '--set-secrets'
      - 'DATABASE_URL=DATABASE_URL:latest,GOOGLE_CLIENT_ID=GOOGLE_CLIENT_ID:latest,GOOGLE_CLIENT_SECRET=GOOGLE_CLIENT_SECRET:latest,JWT_SECRET=JWT_SECRET:latest,SESSION_SECRET=SESSION_SECRET:latest,HUME_API_KEY=HUME_API_KEY:latest,HUME_SECRET_KEY=HUME_SECRET_KEY:latest,HUME_CONFIG_ID=HUME_CONFIG_ID:latest,GOOGLE_CLOUD_PROJECT_ID=GOOGLE_CLOUD_PROJECT_ID:latest,GOOGLE_CLOUD_STORAGE_BUCKET=GOOGLE_CLOUD_STORAGE_BUCKET:latest'
      - '--memory'
      - '1Gi'
      - '--cpu'
      - '1'
      - '--max-instances'
      - '10'
      - '--concurrency'
      - '10'
      - '--timeout'
      - '1800'

  # Deploy frontend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'ora-frontend'
      - '--image'
      - 'gcr.io/$PROJECT_ID/ora-frontend:$BUILD_ID'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--memory'
      - '512Mi'
      - '--cpu'
      - '1'
      - '--max-instances'
      - '5'

images:
  - 'gcr.io/$PROJECT_ID/ora-backend:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/ora-frontend:$BUILD_ID'
